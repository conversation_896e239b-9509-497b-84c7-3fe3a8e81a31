{"welcome": "欢迎", "home": {"title": "让 AI 助力快速实现您的创意", "explore_azure": "探索 Azure", "start_using": "开始使用 Azure 产品和服务将你的想法付诸行动", "view_all_products": "查看所有产品", "customer_innovation": "了解这些客户如何使用 Azure 进行创新", "learn_more": "了解详细信息", "global_infrastructure": "Azure 全球基础结构", "global_description": "Azure 公布了 60 多个区域，超过任何其他云提供商，使用户可以轻松选择适合自己及客户的数据中心和区域。", "products_by_region": "按区域查看产品可用性", "global_explore": "全球探索"}, "nav": {"products_pricing": "产品和定价", "azure_docs": "Azure 文档", "azure_market": "Azure 市场", "support_plans": "Azure 支持计划"}, "products": {"virtual_machine": {"title": "虚拟机", "description": "几秒钟内即可创建 Linux 和 Windows 虚拟机 (VM)，从而节省成本。"}, "sql_managed": {"title": "Azure SQL 托管实例", "description": "通过云中始终保持最新状态的托管 SQL 实例实现 SQL Server 应用程序的现代化。"}, "machine_learning": {"title": "Azure 机器学习", "description": "为端到端机器学习生命周期使用企业级 AI 服务。"}, "kubernetes": {"title": "Azure Kubernetes 服务 (AKS)", "description": "在托管 Kubernetes 上部署和缩放容器。"}, "cosmos_db": {"title": "Azure Cosmos DB", "description": "生成或现代化可缩放的高性能应用。"}, "blob_storage": {"title": "Azure Blob 存储", "description": "适用于云原生工作负载、存档、数据湖、高性能计算和机器学习的可大规模缩放且安全的对象存储。"}, "synapse_analytics": {"title": "Azure Synapse Analytics", "description": "加快获取企业数据仓库和大数据系统见解的时间。"}, "defender_cloud": {"title": "Microsoft Defender for Cloud", "description": "使用从代码到云的集成安全性保护多云和混合环境。"}}}