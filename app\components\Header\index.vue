<script lang="ts" setup>
const localePath = useLocalePath()
const items = ref([
  {
    key: 'pricing',
    label: '产品和定价',
    to: computed(() => localePath('pricing'))
  },
  {
    key: 'documents',
    label: 'Azure 文档',
    to: 'https://docs.azure.cn/zh-cn/?product=popular',
    target: '_blank'
  },
  {
    key: 'market',
    label: 'Azure 市场',
    to: 'https://market.azure.cn/',
    target: '_blank'
  },
  {
    key: 'support&plan',
    label: 'Azure 支持计划',
    to: 'https://support.azure.cn/zh-cn/support/plans/',
    target: '_blank'
  }
])

const showMobelNav = ref(false)


</script>

<template>
  <header class="bg-[#1A1A1A] relative">
    <div class="w-[90%] mx-auto flex justify-between py-4">
      <div class="flex gap-x-12 items-center">
        <div>
          <NuxtLink :to="localePath('index')">
            <img src="~/assets/images/logo.svg" alt="logo" class="block w-80 h-6">
          </NuxtLink>
        </div>
        <div class="hidden lg:flex gap-8">
          <NuxtLink v-for="item in items" :key="item.key" :to="item.to" :target="item.target"
            class="block h-7 pb-2 text-4 cursor-pointer text-white hover:border-b-2 hover:text-[#0078d4] hover:border-b-[#0078d4]">
            {{ item.label }}
          </NuxtLink>

        </div>
      </div>
      <div>
        <img src="~/assets/images/navigation.svg" alt="mobel nav-icon" class="lg:hidden"
          @click="showMobelNav = !showMobelNav">
        <div
          class="text-white border-2 border-white py-[11px] px-[14px] cursor-pointer leading-[normal] hover:bg-white hover:text-black max-lg:hidden">
          登录Azure账户</div>
      </div>


    </div>
    <div :class="['grid-expand', 'absolute', 'z-999', 'w-full', showMobelNav && 'open']">
      <div>
        <div class="bg-black w-full flex flex-col gap-2.5 px-4 py-4 lg:hidden">
          <NuxtLink v-for="item in items" :key="item.key" :to="item.to"
            class="block text-4 text-white hover:border-b-2 hover:text-[#0078d4] hover:border-b-[#0078d4]">
            {{ item.label }}
          </NuxtLink>
          <div
            class="w-full text-white border-2 border-white mt-2 py-[11px] text-center cursor-pointer leading-[normal] hover:bg-white hover:text-black">
            登录Azure账户</div>
        </div>
      </div>
    </div>
  </header>
</template>

<style lang="css" scoped>
.grid-expand {
  display: grid;
  grid-template-rows: 0fr;
  transition: grid-template-rows .35s ease;
}

.grid-expand.open {
  grid-template-rows: 1fr;
}

.grid-expand>div {
  overflow: hidden;
}
</style>
