// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  modules: ['@nuxt/eslint', '@nuxt/image', '@nuxt/ui', '@nuxtjs/i18n'],
  css: ['~/assets/css/main.css'],
  ui: {
    fonts: false,
  },
  icon: {
    customCollections: [{
      prefix: 'myicons',
      dir: './app/assets/icons'
    }]
  },
  image: {},
  i18n: {
    langDir: 'locales',
    defaultLocale: 'zh-cn',
    locales: [
      { code: 'en-us', name: 'English', file: 'en.json' },
      { code: 'zh-cn', name: '中文简体', file: 'zh.json' }
    ],
    strategy: 'prefix_except_default', // 路由策略
    detectBrowserLanguage: false
  }
})