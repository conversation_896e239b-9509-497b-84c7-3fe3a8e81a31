<script lang="ts" setup>
const {t} = useI18n()
const localePath = useLocalePath()
</script>

<template>
  <div>
    <div
      class="relative min-h-80 bg-[#E1F0F5] bg-[url(~/assets/images/banner-mb.png)] lg:bg-[url(~/assets/images/banner-pc.png)] bg-no-repeat bg-size-[auto_320px] bg-right">
      <div class="w-[90%] mx-auto min-h-80 flex items-center max-2xl:px-6">
        <div>
          <h1 class="max-sm:w-[70%] text-3xl lg:text-5xl font-bold mb-10">让 AI 助力快速实现您的创意</h1>
          <div class="inline-block py-3 px-15 border border-[#0078d4] bg-[#0078d4]">
            <NuxtLink :to="localePath('pricing')" class="text-white">探索 Azure {{ t('welcome') }}</NuxtLink>
          </div>
        </div>
      </div>
    </div>
    <div class="w-[90%] mx-auto mt-15 flex flex-col items-center gap-12">
      <div class="text-center text-3xl lg:text-4xl font-medium">
        开始使用 Azure 产品和服务将你的想法付诸行动
      </div>
      <div class="grid grid-cols-2 xl:grid-cols-4">
        <div class="item-box">
          <UIcon name="i-myicons-01-icon-virtual-machine" class="size-8" />
          <h2>虚拟机</h2>
          <p>几秒钟内即可创建 Linux 和 Windows 虚拟机 (VM)，从而节省成本。</p>
        </div>
        <div class="item-box">
          <UIcon name="i-myicons-02-icon-sql-managed-instance" class="size-8" />
          <h2>Azure SQL 托管实例</h2>
          <p>通过云中始终保持最新状态的托管 SQL 实例实现 SQL Server 应用程序的现代化。</p>
        </div>
        <div class="item-box">
          <UIcon name="i-myicons-03-icon-machine-learning" class="size-8" />
          <h2>Azure 机器学习</h2>
          <p>为端到端机器学习生命周期使用企业级 AI 服务。</p>
        </div>
        <div class="item-box">
          <UIcon name="i-myicons-04-icon-kubernetes-service" class="size-8" />
          <h2>Azure Kubernetes 服务 (AKS)</h2>
          <p>在托管 Kubernetes 上部署和缩放容器。</p>
        </div>
        <div class="item-box">
          <UIcon name="i-myicons-05-icon-zzure-cosmos-db" class="size-8" />
          <h2>Azure Cosmos DB</h2>
          <p>生成或现代化可缩放的高性能应用。</p>
        </div>
        <div class="item-box">
          <UIcon name="i-myicons-06-icon-blob-storage" class="size-8" />
          <h2>Azure Blob 存储</h2>
          <p>适用于云原生工作负载、存档、数据湖、高性能计算和机器学习的可大规模缩放且安全的对象存储。</p>
        </div>
        <div class="item-box">
          <UIcon name="i-myicons-07-icon-azure-synapse-analytics" class="size-8" />
          <h2>Azure Synapse Analytics</h2>
          <p>加快获取企业数据仓库和大数据系统见解的时间。</p>
        </div>
        <div class="item-box">
          <UIcon name="i-myicons-08-icon-microsoft-defender-for-cloud" class="size-8" />
          <h2>Microsoft Defender for Cloud</h2>
          <p>使用从代码到云的集成安全性保护多云和混合环境。</p>
        </div>
      </div>
      <NuxtLink :to="localePath('pricing')" class="py-3 px-15 bg-[#0078d4] text-white">
        查看所有产品
      </NuxtLink>
    </div>
    <div
      class="mt-[48px] w-full min-h-[254px] bg-[url(~/assets/images/case.png)] max-lg:bg-[url(~/assets/images/case-mb.png)] bg-no-repeat bg-cover aspect-[780/400] lg:aspect-[1920/254] flex flex-col gap-6 justify-center items-center">
      <div
        class="max-lg:bg-transparent bg-black/50 h-full w-full p-15 flex flex-col justify-center items-center gap-12">
        <h2 class="max-lg:text-3xl lg:text-[40px] text-white font-normal text-center">了解这些客户如何使用 Azure 进行创新</h2>
        <NuxtLink
          to="https://customers.microsoft.com/en-us/search?sq=&ff=story_product_categories%26%3EAzure%26%26story_country_region%26%3EAsia%26%26story_country%26%3EAsia%2FChina&p=0"
          class="text-white hover:text-blue-700" target="_blank">了解详细信息 ></NuxtLink>
      </div>
    </div>
    <div class="w-full py-15 bg-[#1A1A1A] flex flex-col gap-6 justify-center items-center">
      <div class="h-full w-full">
        <div class="flex flex-col justify-center items-center gap-6 text-white text-center px-15 pb-10">
          <div class="mb-10 flex flex-col gap-4">
            <h2 class="max-lg:text-3xl text-[40px] font-normal">Azure 全球基础结构</h2>
            <p>Azure 公布了 60 多个区域，超过任何其他云提供商，使用户可以轻松选择适合自己及客户的数据中心和区域。</p>
          </div>

          <div class="flex gap-5">
            <NuxtLink to="https://azure.microsoft.com/zh-cn/explore/global-infrastructure/products-by-region/"
              class=" hover:text-blue-700" target="_blank">按区域查看产品可用性 ></NuxtLink>
            <NuxtLink to="https://datacenters.microsoft.com/" class=" hover:text-blue-700" target="_blank">全球探索 >
            </NuxtLink>
          </div>
        </div>

        <NuxtImg src="/map.png" />
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
@reference "~/assets/css/main.css";

.item-box {
  @apply flex-1 flex flex-col items-start gap-2 pl-2.5 pr-4 py-4 border-r border-b border-gray-300 relative float-left lg:w-auto lg:pl-4;
}

.item-box h2 {
  @apply font-bold;
}

/* :before 竖线 */
.item-box::before {
  content: "";
  @apply absolute top-[-1px] bottom-[-1px] right-[-1px] w-px bg-gradient-to-b from-transparent to-gray-300;
}

/* :after 横线（默认空，nth-child 里覆盖） */
.item-box::after {
  content: "";
  @apply absolute bottom-[-1px] left-[-1px] right-[-1px] h-px;
}

/* ≥1280px 时的 nth-child 规则 */
@media (min-width: 1280px) {
  .item-box:nth-child(-n+3) {
    @apply border-r-transparent;
  }

  .item-box:nth-child(4n) {
    @apply border-r-0;
  }

  .item-box:nth-child(4n)::before {
    @apply bg-none;
  }

  .item-box:nth-child(1),
  .item-box:nth-child(4),
  .item-box:nth-child(5),
  .item-box:nth-child(8) {
    @apply border-b-transparent;
  }

  .item-box:nth-child(1)::after {
    @apply bg-gradient-to-r from-transparent to-gray-300;
  }

  .item-box:nth-child(4)::after {
    @apply bg-gradient-to-r from-gray-300 to-transparent;
  }

  .item-box:nth-child(5)::after {
    @apply bg-gradient-to-r from-transparent to-gray-300;
  }

  .item-box:nth-child(8)::after {
    @apply bg-gradient-to-r from-gray-300 to-transparent;
  }
}

/* ≤1280px 时的 nth-child 规则 */
@media (max-width: 1280px) {
  .item-box:nth-child(1) {
    @apply border-r-transparent;
  }

  .item-box:nth-child(n+1) {
    @apply border-b-transparent;
  }

  .item-box:nth-child(n+1)::after {
    @apply bg-gradient-to-r from-transparent to-gray-300;
  }

  .item-box:nth-child(2n) {
    @apply border-r-0 border-b-transparent;
  }

  .item-box:nth-child(2n)::after {
    @apply bg-gradient-to-r from-gray-300 to-transparent;
  }

  .item-box:nth-child(2n)::before {
    @apply bg-none;
  }
}
</style>
