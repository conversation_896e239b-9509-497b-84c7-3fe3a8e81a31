<script lang="ts" setup>
const { locale, locales } = useI18n()
const switchLocalePath = useSwitchLocalePath()

// 语言选项 - 优先使用动态数据，否则使用静态数据
const languageOptions = computed(() => {
    return locales.value.map(lang => ({
      label: lang.name,
      value: lang.code,
    }))
})

// 切换语言
const changeLanguage = (langCode: 'zh-cn'|'en-us') => {
  // 获取切换语言后的正确路径
  const path = switchLocalePath(langCode)
  // 跳转到新路径
  navigateTo(path)
}
</script>

<template>
  <div>
    <USelect 
      v-model="locale" 
      :items="languageOptions" 
      icon="myicons:map" 
      variant="subtle" 
      size="xs"
      class="w-28 bg-transparent text-white hover:bg-transparent cursor-pointer"
      @update:model-value="changeLanguage"
    />
  </div>
</template>
