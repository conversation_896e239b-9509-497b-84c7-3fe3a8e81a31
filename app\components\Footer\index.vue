<script lang="ts" setup>
const columnOne = ref([
  {
    "title": "产品和定价",
    "to": "https://www.azure.cn/pricing/",
    "target": "_self"
  },
  {
    "title": "Azure 混合权益（Azure Hybrid Benefit）",
    "to": "https://www.azure.cn/pricing/hybrid-benefit/",
    "target": "_blank"
  },
  {
    "title": "Azure 文档   >",
    "to": "https://docs.azure.cn/zh-cn/?product=popular",
    "target": "_blank"
  },
  {
    "title": "获取支持   >",
    "to": "https://support.azure.cn/zh-cn/support/contact/",
    "target": "_blank"
  },
  {
    "title": "Azure 支持计划   >",
    "to": "https://support.azure.cn/zh-cn/support/plans/",
    "target": "_blank"
  },
  {
    "title": "新商务模式指南   >",
    "to": "https://learn.21vbluecloud.com/zh-cn/home",
    "target": "_blank"
  },
])

const columnTwo = ref([
  {
    "title": "Azure 门户   >",
    "to": "https://portal.azure.cn/",
    "target": "_blank"
  },
  {
    "title": "企业门户网站   >",
    "to": "https://ea.azure.cn/",
    "target": "_blank"
  },
  {
    "title": "帐户中心   >",
    "to": "https://account.windowsazure.cn/Subscriptions",
    "target": "_blank"
  },
  {
    "title": "Microsoft Azure   >",
    "to": "https://azure.microsoft.com/zh-cn/",
    "target": "_blank"
  },
  {
    "title": "可用产品(按区域)   >",
    "to": "https://azure.microsoft.com/zh-cn/explore/global-infrastructure/products-by-region/",
    "target": "_blank"
  },
  {
    "title": "Azure 状态   >",
    "to": "https://azure.status.microsoft/zh-cn/status",
    "target": "_blank"
  },
  {
    "title": "Azure 地域   >",
    "to": "https://datacenters.microsoft.com/",
    "target": "_blank"
  },
])

const columnThree = ref([
  {
    "title": "服务级别协议 (SLA)",
    "to": "https://www.azure.cn/support/legal/sla/",
    "target": "_self"
  },
  {
    "title": "法律信息",
    "to": "https://www.azure.cn/support/legal/",
    "target": "_self"
  },
  {
    "title": "ICP 备案",
    "to": "https://www.azure.cn/support/icp/",
    "target": "_self"
  },
  {
    "title": "公安备案",
    "to": "https://www.azure.cn/support/announcement/public-security-registration/",
    "target": "_self"
  },
  {
    "title": "在线服务隐私声明   >",
    "to": "https://www.21vbluecloud.com/ostpt/",
    "target": "_blank"
  },
  {
    "title": "信任中心   >",
    "to": "https://www.trustcenter.cn/",
    "target": "_blank"
  }
])

</script>

<template>
  <footer class="bg-[#1A1A1A] text-xs">
    <div class="w-[90%] mx-auto py-6 flex flex-col gap-4 text-white">
      <div class="flex justify-between">
        <div class="flex-1 flex flex-col gap-4 max-sm:mx-8">
          <div>
            <div class="mb-1 text-gray-400 text-[12px]">Follow us</div>
            <div class="bg-gray-100 p-1 text-center w-28">
              <img src="~/assets/images/qrcode-a.svg" alt="follow us" class="w-full">
              <div class="text-black text-[10px] mt-1">Follow us</div>
            </div>
          </div>
          <div>
            <div class="mb-1 text-gray-400 text-[12px]">Get support fast</div>
            <div class="bg-gray-100 p-1 text-center w-28">
              <img src="~/assets/images/qrcode-b.svg" alt="get support fast" class="w-full">
              <div class="text-black text-[10px] mt-1">Get support fast</div>
            </div>
          </div>
        </div>
        <div class="flex-3 flex flex-col gap-4 md:flex-row">
          <div class="flex-1 flex flex-col gap-4">
            <NuxtLink v-for="item in columnOne" :key="item.to" :to="item.to" :target="item.target">{{ item.title }}
            </NuxtLink>
          </div>
          <div class="flex-1 flex flex-col gap-4">
            <NuxtLink v-for="item in columnTwo" :key="item.to" :to="item.to" :target="item.target">{{ item.title }}
            </NuxtLink>
          </div>
          <div class="flex-1 flex flex-col gap-4">
            <NuxtLink v-for="item in columnThree" :key="item.to" :to="item.to" :target="item.target">{{ item.title }}
            </NuxtLink>
          </div>
        </div>
      </div>
      <div class="mx-auto flex gap-5 items-center max-sm:flex-col max-sm:items-start max-sm:mx-8">
        <div>
         <SelectLang />
        </div>
        <div>
          <NuxtLink to="https://beian.miit.gov.cn/" target="_blank">沪ICP备13015306号-25</NuxtLink>
        </div>
        <div class="flex gap-2 items-center">
          <img src="~/assets/images/beian.png">
          <NuxtLink to="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011502002224" target="_blank">
            沪公网安备 31011502002224号</NuxtLink>
        </div>
        <div>
          <NuxtLink to="https://www.azure.cn/support/legal/privacy-statement/">隐私</NuxtLink>
        </div>
        <div>
          <NuxtLink to="https://www.azure.cn/support/legal/privacy-statement/">
            <img src="~/assets/images/logo.svg" class="h-5">
          </NuxtLink>
        </div>
      </div>
    </div>
  </footer>
</template>
