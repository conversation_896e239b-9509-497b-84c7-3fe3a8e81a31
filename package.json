{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "1.7.1", "@nuxt/image": "1.10.0", "@nuxt/ui": "3.3.0", "@nuxtjs/i18n": "^10.0.3", "eslint": "^9.0.0", "nuxt": "^4.0.1", "typescript": "^5.6.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "packageManager": "pnpm@8.15.6+sha1.8105075ad0aa306138be128c017e9c41e28ecffa"}